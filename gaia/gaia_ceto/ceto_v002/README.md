# CETO Chat System

CETO is an enhanced chat system with chronological storage for thousands of conversations. It provides a simple terminal interface for interacting with various LLM providers.

## Features

- Terminal-based chat interface
- Support for multiple LLM providers (Mock, OpenAI, Anthropic, MCP SSE, MCP HTTP)
- Chronological storage of conversations
- Conversation management (create, load, save, delete)
- Command history and navigation

## Requirements

- Python 3.8+
- Required packages:
  - For OpenAI: `pip install openai`
  - For Anthropic: `pip install anthropic`

## Running the Chat Terminal

The main entry point is `chat_term.py`. Due to the project structure, you need to set the Python path correctly:

```bash
# From the project root directory
python -m gaia.gaia_ceto.ceto_v002.chat_term [options]


#  regular usage
python -m gaia.gaia_ceto.ceto_v002.chat_term  --llm mcp-http  --mcp-http-server http://localhost:9000/mcp
```

### Command-line Options

- `--storage-dir DIR`: Directory to store conversations (default: `./conversations`)
- `--user-id ID`: User ID to associate with conversations (default: system username)
- `--llm PROVIDER`: LLM provider to use (choices: "mock", "openai", "anthropic", "mcp", "mcp-http", default: "mock")
- `--model NAME`: Specific model name to use with the selected LLM provider
- `--mcp-server URL`: MCP SSE server URL when using the "mcp" provider (default: "http://0.0.0.0:9000/sse")
- `--mcp-http-server URL`: MCP HTTP server URL when using the "mcp-http" provider (default: "http://0.0.0.0:9000/mcp")

### Examples

```bash
# Using the Mock LLM (default)
python -m gaia.gaia_ceto.ceto_v002.chat_term

# Using OpenAI
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm openai --model gpt-3.5-turbo

# Using Anthropic
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm anthropic --model claude-3-sonnet-20240229

# Using MCP with SSE protocol
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp --mcp-server http://0.0.0.0:9000/sse

# Using MCP with HTTP protocol
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --mcp-http-server http://0.0.0.0:9000/mcp

# Using MCP with a custom server URL
python -m gaia.gaia_ceto.ceto_v002.chat_term --llm mcp-http --model http://example.com:9000/mcp

# Specifying a storage directory
python -m gaia.gaia_ceto.ceto_v002.chat_term --storage-dir /var/lib/gaia/GAIA_FS/ceto_conversations
```

### API Keys

For OpenAI and Anthropic, you need to set the appropriate API keys as environment variables:

```bash
export OPENAI_API_KEY=your_openai_api_key
export ANTHROPIC_API_KEY=your_anthropic_api_key
```

## LLM Providers

CETO supports the following LLM providers:

### Mock LLM

The Mock LLM is a simple implementation that echoes back the user's input with a prefix. It's useful for testing and development without using API credits.

### OpenAI

The OpenAI implementation supports various GPT models:

- `gpt-3.5-turbo`: A good balance of capability and cost
- `gpt-4`: More capable but more expensive
- Other models as available in the OpenAI API

Configuration:
- Set the `OPENAI_API_KEY` environment variable
- Use the `--model` parameter to specify the model name

### Anthropic

The Anthropic implementation supports Claude models:

- `claude-3-sonnet-20240229`: A good balance of capability and cost
- `claude-3-opus-20240229`: More capable but more expensive
- Other Claude models as available

Configuration:
- Set the `ANTHROPIC_API_KEY` environment variable
- Use the `--model` parameter to specify the model name

### MCP SSE

The MCP SSE implementation connects to a Model Control Protocol server using Server-Sent Events (SSE) for streaming responses. This allows the use of Claude with tool-calling capabilities.

Configuration:
- Use the `--llm mcp` parameter to select the MCP SSE provider
- Use the `--mcp-server` parameter to specify the server URL (default: "http://0.0.0.0:9000/sse")
- Alternatively, use the `--model` parameter to specify a custom server URL

### MCP HTTP

The MCP HTTP implementation connects to a Model Control Protocol server using HTTP for streaming responses. This provides an alternative to the SSE protocol with the same functionality.

Configuration:
- Use the `--llm mcp-http` parameter to select the MCP HTTP provider
- Use the `--mcp-http-server` parameter to specify the server URL (default: "http://0.0.0.0:9000/mcp")
- Alternatively, use the `--model` parameter to specify a custom server URL

## Available Commands

Once the chat terminal is running, you can use the following commands:

| Command | Description |
|---------|-------------|
| `/help` | Show available commands |
| `/new [title]` | Create a new conversation with optional title |
| `/list [year] [month]` | List conversations, optionally filtered by year/month |
| `/load <id\|path>` | Load a conversation by ID or path |
| `/save` | Save the current conversation |
| `/delete <id\|path>` | Delete a conversation by ID or path |
| `/info` | Show information about the current conversation |
| `/stats` | Show statistics about stored conversations |
| `/clear` | Clear the terminal screen |
| `/exit` or `/quit` | Exit the chat terminal |

## Chatting

To chat with the assistant, simply type your message and press Enter (without a leading slash).

Example:
```
[My Conversation] > Hello, how are you today?
```

## Conversation Storage

Conversations are stored in a chronological directory structure:

```
storage_dir/
├── YYYY/
│   ├── MM/
│   │   ├── conversation_id_1.json
│   │   ├── conversation_id_2.json
│   │   └── ...
│   └── ...
└── ...
```

Each conversation is stored as a JSON file with the following structure:

```json
{
  "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
  "user_id": "username",
  "title": "My Conversation",
  "metadata": {},
  "messages": [
    {
      "role": "system",
      "content": "How can I help you today?",
      "timestamp": "2023-05-01T12:34:56.789012"
    },
    {
      "role": "user",
      "content": "Hello, how are you today?",
      "timestamp": "2023-05-01T12:35:00.123456"
    },
    {
      "role": "assistant",
      "content": "I'm doing well, thank you for asking! How can I assist you today?",
      "timestamp": "2023-05-01T12:35:05.678901"
    }
  ],
  "created_at": "2023-05-01T12:34:56.789012",
  "updated_at": "2023-05-01T12:35:05.678901"
}
```


# Running Tests

## Chatobj Tests
```bash
python -m unittest gaia.gaia_ceto.ceto_v002.test_chatobj
```

# MCP Client Libraries

CETO supports two different client libraries for connecting to MCP servers:

## MCP SSE Client Library

The MCP SSE client library uses Server-Sent Events (SSE) for streaming responses from the MCP server. This is the original implementation and is located in:

```
gaia/gaia_ceto/proto_mcp/mcp_sse_clientlib.py
```

## MCP HTTP Client Library

The MCP HTTP client library uses HTTP for streaming responses from the MCP server. This is an alternative implementation that provides the same functionality but uses a different protocol. It is located in:

```
gaia/gaia_ceto/proto_mcp_http/mcp_http_clientlib.py
```

Both client libraries implement the same interface and can be used interchangeably in the chat terminal and web UI.
