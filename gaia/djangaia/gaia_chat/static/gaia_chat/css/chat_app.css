/* Chat App Styles */

:root {
    --primary-color: #f4f5ef;
    --secondary-color: #ccc;
    --background-color: #f4f4f4;
    --text-color: #333;
    --light-text: #666;
    --border-color: #ddd;
    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-family: Verdana, sans-serif;
    line-height: 1.6;
    background-color: var(--background-color);
    color: var(--text-color);
}

.app-container {
    display: flex;
    height: 100vh;
}

.sidebar {
    width: 300px;
    background-color: var(--primary-color);
    color: #1d1d1d;
    padding: 1rem;
    overflow-y: auto;
}

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-right: v-bind('chatContext && activeConversation ? "450px" : "0"');
}

.chat-container {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    background-color: white;
}

.chat-inner {
    margin: 0 auto;
    max-width: 800px;
    width: 94%;
    padding: 1rem;
    overflow-y: auto;
    background-color: white;
}

.chat-inner h2 {
    background: url('');
    margin-bottom: 2rem;
}

.input-container {
    padding: 1rem;
    background-color: #f9f9f9;
    border-top: 1px solid var(--border-color);
}

.sidebar h2 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.sidebar h3 {
    margin: 1rem 0 0.5rem;
    font-size: 1.2rem;
    color: #1d1d1d;
}

.conversation-list {
    margin-top: 1rem;
}

.conversation-item {
    padding: 0rem 0.5rem 0rem 0.5rem;
    margin-bottom: 0.2rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: background-color 0.2s;
}

.conversation-content {
    flex: 1;
    cursor: pointer;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0.2rem 0;
}

.delete-button {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.5);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 1.5rem;
    width: 1.5rem;
    border-radius: 50%;
    transition: all 0.2s;
}

.delete-button:hover {
    background-color: rgba(255, 0, 0, 0.2);
    color: white;
}

.delete-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.loading-small {
    display: inline-block;
    width: 0.8rem;
    height: 0.8rem;
    border: 2px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #666;
    animation: spin 1s ease-in-out infinite;
}

.conversation-item:hover {
    background-color: #ccc;
}

.conversation-item.active {
    background-color: var(--secondary-color);
}

.message {
    margin-bottom: 0.5rem;
    /*padding: 0.5rem 1rem;*/
    padding: 0rem 0.5rem;
    border-radius: 10px;
}

.message-user {
    background-color: #f0f0f0;
}

.message-assistant {

}

.message-system {
    margin: 0 1rem;
    font-style: italic;
}

.message-role {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.message-content {
    white-space: pre-wrap;
    padding: 0.5rem;
    line-height: 1.3;
}

/* Global override for all HTML elements in message content */
.message-content > * {
    margin: 0 !important;
}

.message-content > *:not(:last-child) {
    margin-bottom: 0.1rem !important;
}

/* Aggressively reduce whitespace in markdown-rendered content */
.message-content p {
    margin: 0.1rem 0 !important;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
    margin: 0.3rem 0 0.1rem 0 !important;
    line-height: 1.2 !important;
}



.message-content li {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
}

.message-content li p {
    margin: 0 !important;
    padding: 0 !important;
    display: inline;
}

.message-content blockquote {
    margin: 0.1rem 0 !important;
    padding-left: 0.8rem !important;
    border-left: 3px solid #ddd;
}

.message-content pre {
    margin: 0.1rem 0 !important;
    padding: 0.4rem !important;
    background-color: #f5f5f5;
    border-radius: 4px;
    overflow-x: auto;
}

.message-content code {
    background-color: #f5f5f5;
    padding: 0.05rem 0.2rem !important;
    border-radius: 2px;
}

/* Remove default margins from all elements inside message content, except lists */
.message-content *:not(ul):not(ol):not(li) {
    margin-top: 0 !important;
    margin-bottom: 0.05rem !important;
}

/* Nuclear option: Override ALL list spacing with maximum specificity */
.message-content ul,
.message-content ol {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 1.2rem !important;
    padding-right: 0 !important;
    /* Force override browser default ul/ol styles */
    -webkit-margin-before: 0 !important;
    -webkit-margin-after: 0 !important;
    -webkit-margin-start: 0 !important;
    -webkit-margin-end: 0 !important;
    -webkit-padding-start: 1.2rem !important;
    margin-block-start: 0 !important;
    margin-block-end: 0 !important;
    margin-inline-start: 0 !important;
    margin-inline-end: 0 !important;
    padding-inline-start: 1.2rem !important;
}

/* Make bullet points full black circles */
.message-content ul {
    list-style-type: disc !important;
}

.message-content ul li {
    list-style-type: disc !important;
}

/* Make numbered list numbers bold */
.message-content ol {
    list-style-type: decimal !important;
    font-weight: normal !important;
}

.message-content ol li {
    font-weight: normal !important;
}

.message-content ol li::marker {
    font-weight: bold !important;
    color: #000 !important;
}

/* Nuclear option: Override ALL list item spacing */
.message-content ul li,
.message-content ol li,
.message-content li {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    line-height: 1.1 !important;
}

/* Ensure absolutely no spacing between consecutive list items */
.message-content li + li {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Ultra-specific targeting for ul bottom spacing issue */
.message-content > ul,
.message-content > ol,
.message-content ul:last-child,
.message-content ol:last-child {
    margin-bottom: 0 !important;
    margin-block-end: 0 !important;
    -webkit-margin-after: 0 !important;
}

/* Target any ul/ol that comes before other elements */
.message-content ul + *,
.message-content ol + * {
    margin-top: 0 !important;
}

.input-box {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
}

.button {
    padding: 0.5rem 1rem;
    background-color: #1d1d1d;
    color: white;
    border: none;
    /*border-radius: 4px;*/
    cursor: pointer;
    font-size: 1rem;
    margin-top: 0.5rem;
}

.button:hover {
    background-color: #888;
}

.button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.select-container {
    margin-bottom: 1rem;
}

.select-container label {
    display: block;
    margin-bottom: 0.25rem;
    color: #666;
}

.select-container select {
    width: 100%;
    padding: 0.5rem;
    border-radius: 4px;
    background-color: white;
    border: 1px solid var(--border-color);
}

.alert {
    padding: 0.5rem;
    margin-bottom: 1rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.alert-warning {
    background-color: #fff8e1;
    margin-top: 0.5rem;
    border-left: 4px solid var(--warning-color);
}

.alert-error {
    background-color: #ffebee;
    border-left: 4px solid var(--error-color);
}

.alert-success {
    background-color: #e8f5e9;
    border-left: 4px solid var(--success-color);
}

.loading {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
    margin-left: 0.5rem;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: var(--light-text);
}

.user-info {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #caf22a;
    border-radius: 0px;
}

.user-info p {
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.user-links {
    display: flex;
    justify-content: space-between;
}

.user-link {
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.user-link:hover {
    color: var(--text-color);
    text-decoration: underline;
}

.logo {
    margin-bottom: 1rem;
}

.context-content-main {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.chat-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    width: 450px; /* Increased from 300px to 450px (150% wider) */
    padding: 20px;
    height: 100vh;
    background: #f8f9fa;
    border-left: 1px solid #ddd;
    display: flex;
    flex-direction: column;
    z-index: 1000;
}

.chat-sidebar-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
}

.chat-sidebar-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2em;
    font-weight: 600;
}

.chat-messages-container {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

/* Completely redesigned scrollbar handling */
.chat-messages-container {
    flex: 1;
    overflow-y: auto;
    margin-bottom: 15px;
    display: flex;
    flex-direction: column;
}

/* Make message containers fully fluid */
.message-compact {
    margin-bottom: 8px;
    padding: 5px;
    border-radius: 5px;
    font-size: 0.9rem;
    word-wrap: break-word;
    overflow-wrap: break-word;
    overflow: visible; /* Ensure no scrollbars appear */
    width: 100%;
}

.message-role-compact {
    font-weight: bold;
    font-size: 0.8rem;
}

.message-content-compact {
    white-space: pre-wrap;
    overflow: visible !important; /* Force no scrollbars */
    font-size: 0.9rem;
    max-height: none;
    width: 100%;
    display: block;
    line-height: 1.2;
}

/* Global override for all HTML elements in compact content */
.message-content-compact > * {
    margin: 0 !important;
}

.message-content-compact > *:not(:last-child) {
    margin-bottom: 0.05rem !important;
}

/* Aggressively reduce whitespace in compact markdown-rendered content */
.message-content-compact p {
    margin: 0.05rem 0 !important;
}

.message-content-compact h1,
.message-content-compact h2,
.message-content-compact h3,
.message-content-compact h4,
.message-content-compact h5,
.message-content-compact h6 {
    margin: 0.2rem 0 0.05rem 0 !important;
    font-size: 1em !important;
    line-height: 1.1 !important;
}



.message-content-compact li {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.1 !important;
}

.message-content-compact li p {
    margin: 0 !important;
    padding: 0 !important;
    display: inline;
}

.message-content-compact blockquote {
    margin: 0.05rem 0 !important;
    padding-left: 0.6rem !important;
    border-left: 2px solid #ddd;
}

.message-content-compact pre {
    margin: 0.05rem 0 !important;
    padding: 0.3rem !important;
    background-color: #f5f5f5;
    border-radius: 3px;
    overflow-x: auto;
    font-size: 0.8rem;
}

.message-content-compact code {
    background-color: #f5f5f5;
    padding: 0.02rem 0.15rem !important;
    border-radius: 2px;
    font-size: 0.85rem;
}

/* Remove default margins from all elements inside compact content, except lists */
.message-content-compact *:not(ul):not(ol):not(li) {
    margin-top: 0 !important;
    margin-bottom: 0.02rem !important;
}

/* Nuclear option: Override ALL compact list spacing */
.message-content-compact ul,
.message-content-compact ol {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    padding-left: 1rem !important;
    /* Force override browser default ul/ol styles */
    -webkit-margin-before: 0 !important;
    -webkit-margin-after: 0 !important;
    margin-block-start: 0 !important;
    margin-block-end: 0 !important;
}

/* Make compact bullet points full black circles */
.message-content-compact ul {
    list-style-type: disc !important;
}

.message-content-compact ul li {
    list-style-type: disc !important;
}

/* Make compact numbered list numbers bold */
.message-content-compact ol {
    list-style-type: decimal !important;
    font-weight: normal !important;
}

.message-content-compact ol li {
    font-weight: normal !important;
}

.message-content-compact ol li::marker {
    font-weight: bold !important;
    color: #000 !important;
}

/* Make compact bullet points full black circles */
.message-content-compact ul {
    list-style-type: disc !important;
}

.message-content-compact ul li {
    list-style-type: disc !important;
}

/* Nuclear option: Override ALL compact list item spacing */
.message-content-compact ul li,
.message-content-compact ol li,
.message-content-compact li {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    line-height: 1.0 !important;
}

/* Ensure absolutely no spacing between consecutive compact list items */
.message-content-compact li + li {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Ultra-specific targeting for compact ul bottom spacing issue */
.message-content-compact > ul,
.message-content-compact > ol,
.message-content-compact ul:last-child,
.message-content-compact ol:last-child {
    margin-bottom: 0 !important;
    margin-block-end: 0 !important;
    -webkit-margin-after: 0 !important;
}

.message-content-compact ul + *,
.message-content-compact ol + * {
    margin-top: 0 !important;
}

/* Completely disable scrollbars on message content */
.message-content-compact::-webkit-scrollbar,
.message-compact::-webkit-scrollbar {
    width: 0 !important;
    height: 0 !important;
    display: none !important;
}

.message-content-compact,
.message-compact {
    -ms-overflow-style: none !important;  /* IE and Edge */
    scrollbar-width: none !important;  /* Firefox */
}

/* Ensure all content inside messages wraps properly */
.message-content-compact * {
    max-width: 100%;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: pre-wrap;
}

/* Special handling for code blocks */
.message-content-compact pre,
.message-content-compact code {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    max-width: 100% !important;
    overflow: visible !important;
}

/* Ensure tables don't cause horizontal scrolling */
.message-content-compact table {
    max-width: 100%;
    table-layout: fixed;
    width: 100%;
}

.message-content-compact td,
.message-content-compact th {
    word-break: break-word;
}

.input-container-sidebar {
    padding: 10px 0;
    border-top: 1px solid var(--border-color);
}

/* Adjust main content when context sidebar is present */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-right: v-bind('chatContext && activeConversation ? "450px" : "0"');
}

/* Add these styles to handle code blocks and pre-formatted text */
.message-content-compact pre,
.message-content-compact code {
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    overflow-x: visible;
}

/* Ensure images don't overflow */
.message-content-compact img {
    max-width: 100%;
    height: auto;
}

/* Ensure tables don't cause horizontal scrolling */
.message-content-compact table {
    max-width: 100%;
    display: block;
    overflow-x: auto;
}

/* Add styles for context info banner */
.context-info-banner {
    margin: 15px 0;
    padding: 10px 15px;
    background-color: #e8f5e9;
    border-left: 4px solid var(--success-color);
    border-radius: 4px;
}

.context-info-banner p {
    margin: 5px 0;
}

/* Context content styling */
.context-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.context-item {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.context-item:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.context-item h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.3em;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 8px;
}

.context-details {
    margin-bottom: 15px;
}

.context-details p {
    margin: 8px 0;
    display: flex;
    align-items: center;
    font-size: 0.95em;
}

.context-details strong {
    color: #34495e;
    min-width: 90px;
    font-weight: 600;
    margin-right: 8px;
}

.context-description {
    color: #555;
    font-style: italic;
    line-height: 1.5;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #ecf0f1;
}

/* Industry-specific color coding */
.context-item[data-industry="Manufacturing"] h4 {
    border-bottom-color: #e74c3c;
}

.context-item[data-industry="Technology"] h4 {
    border-bottom-color: #3498db;
}

.context-item[data-industry="Renewable Energy"] h4 {
    border-bottom-color: #27ae60;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .context-content {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .context-item {
        padding: 15px;
    }
}

/* Add subtle animation for loading states */
.context-item.loading {
    opacity: 0.7;
    pointer-events: none;
}

/* Improve readability with better typography */
.context-item h4 {
    line-height: 1.3;
}

.context-details p {
    line-height: 1.4;
}

/* JSON fallback styling */
.context-json {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.85em;
    line-height: 1.4;
    overflow-x: auto;
    white-space: pre-wrap;
    color: #495057;
}

/* Add a subtle gradient background to the context area */
.context-content-main {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Sidebar chat styling to match main chat interface */
.sidebar-message {
    margin-bottom: 0.5rem;
    padding: 0rem 0.5rem;
    border-radius: 10px;
}

.sidebar-message.message-user {
    background-color: #f0f0f0;
}

.sidebar-message.message-assistant {
    background-color: transparent;
}

.sidebar-message.message-system {
    margin: 0 1rem;
    font-style: italic;
}

.sidebar-role {
    font-weight: bold;
    margin-bottom: 0.25rem;
    display: block;
}

.sidebar-content {
    white-space: pre-wrap;
    padding: 0.5rem;
    line-height: 1.3;
}

/* Global override for all HTML elements in sidebar content */
.sidebar-content > * {
    margin: 0 !important;
}

.sidebar-content > *:not(:last-child) {
    margin-bottom: 0.1rem !important;
}

/* Aggressively reduce whitespace in sidebar markdown-rendered content */
.sidebar-content p {
    margin: 0.1rem 0 !important;
}

.sidebar-content h1,
.sidebar-content h2,
.sidebar-content h3,
.sidebar-content h4,
.sidebar-content h5,
.sidebar-content h6 {
    margin: 0.3rem 0 0.1rem 0 !important;
    line-height: 1.2 !important;
}



.sidebar-content li {
    margin: 0 !important;
    padding: 0 !important;
    line-height: 1.2 !important;
}

.sidebar-content li p {
    margin: 0 !important;
    padding: 0 !important;
    display: inline;
}

.sidebar-content blockquote {
    margin: 0.1rem 0 !important;
    padding-left: 0.8rem !important;
    border-left: 3px solid #ddd;
}

.sidebar-content pre {
    margin: 0.1rem 0 !important;
    padding: 0.4rem !important;
    background-color: #f5f5f5;
    border-radius: 4px;
    overflow-x: auto;
}

.sidebar-content code {
    background-color: #f5f5f5;
    padding: 0.05rem 0.2rem !important;
    border-radius: 2px;
}

/* Remove default margins from all elements inside sidebar content, except lists */
.sidebar-content *:not(ul):not(ol):not(li) {
    margin-top: 0 !important;
    margin-bottom: 0.05rem !important;
}

/* Nuclear option: Override ALL sidebar list spacing */
.sidebar-content ul,
.sidebar-content ol {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    padding-left: 1.2rem !important;
    /* Force override browser default ul/ol styles */
    -webkit-margin-before: 0 !important;
    -webkit-margin-after: 0 !important;
    margin-block-start: 0 !important;
    margin-block-end: 0 !important;
}

/* Make sidebar bullet points full black circles */
.sidebar-content ul {
    list-style-type: disc !important;
}

.sidebar-content ul li {
    list-style-type: disc !important;
}

/* Make sidebar numbered list numbers bold */
.sidebar-content ol {
    list-style-type: decimal !important;
    font-weight: normal !important;
}

.sidebar-content ol li {
    font-weight: normal !important;
}

.sidebar-content ol li::marker {
    font-weight: bold !important;
    color: #000 !important;
}

/* Nuclear option: Override ALL sidebar list item spacing */
.sidebar-content ul li,
.sidebar-content ol li,
.sidebar-content li {
    margin: 0 !important;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding: 0 !important;
    line-height: 1.1 !important;
}

/* Ensure absolutely no spacing between consecutive sidebar list items */
.sidebar-content li + li {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

/* Ultra-specific targeting for sidebar ul bottom spacing issue */
.sidebar-content > ul,
.sidebar-content > ol,
.sidebar-content ul:last-child,
.sidebar-content ol:last-child {
    margin-bottom: 0 !important;
    margin-block-end: 0 !important;
    -webkit-margin-after: 0 !important;
}

.sidebar-content ul + *,
.sidebar-content ol + * {
    margin-top: 0 !important;
}

.sidebar-input-area {
    padding: 1rem;
    background-color: #f9f9f9;
    border-top: 1px solid var(--border-color);
}

.sidebar-textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.sidebar-button {
    padding: 0.5rem 1rem;
    background-color: #1d1d1d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
}

.sidebar-button:hover {
    background-color: #333;
}

.sidebar-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* Pagination styles */
.pagination-navigation {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.pagination-info {
    display: flex;
    align-items: center;
}

.companies-count {
    font-size: 0.95rem;
    color: #495057;
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #dee2e6;
    background-color: white;
    color: #495057;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    min-width: 60px;
    text-align: center;
}

.pagination-btn:hover:not(:disabled) {
    background-color: #e9ecef;
    border-color: #adb5bd;
    color: #212529;
}

.pagination-btn:disabled {
    background-color: #f8f9fa;
    color: #adb5bd;
    cursor: not-allowed;
    border-color: #e9ecef;
}

.page-info {
    font-weight: 600;
    color: #212529;
    margin: 0 0.75rem;
    font-size: 0.9rem;
    white-space: nowrap;
}

.companies-container {
    margin-top: 0;
}

/* Responsive pagination */
@media (max-width: 768px) {
    .pagination-navigation {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .pagination-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .pagination-btn {
        min-width: 50px;
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }

    .page-info {
        margin: 0 0.5rem;
        font-size: 0.85rem;
    }
}
